[{"name": "startup", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "startup"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "author": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "author"}, "optional": true}, "views": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "category": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "pitch": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "author", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "author"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "id": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "username": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "image": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bio": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "markdown", "type": "type", "value": {"type": "string"}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]