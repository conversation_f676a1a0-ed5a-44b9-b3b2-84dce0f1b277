import React from "react";
import Form from "next/form";
import SearchFormReset from "./SearchFormReset";
import { Search } from "lucide-react";

const SearchForm = ({query}: { query?: string }) => {
  //Form itself is server side rendered component but the SearchFormReset is extracted to client side to enable client side navigation
  return (
    <Form action="/" scroll={false} className="search-form">
      <input
        name="query"
        defaultValue={query}
        className="search-input"
        placeholder="Search startups"
      />

      <div className="flex gap-2">
        {query && <SearchFormReset />}

        <button type="submit" className="search-btn text-white" >
          <Search className="size-5"/>
        </button>
      </div>
    </Form>
  );
};

export default SearchForm;
