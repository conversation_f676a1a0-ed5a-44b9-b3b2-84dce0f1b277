import React from 'react'
import Ping from './Ping'
import {client} from "@sanity/lib/client"
const View = async ({id}: [id : string]) => {
  const {views : await client.with}
  return (
    <div className='view-container'>
      <div className='absolute -top-2 -right-2'>
        <Ping />
      </div>
      <p className='view-text'>
        <span className='font-black'>100 views</span>
      </p>
    </div>
  )
}

export default View