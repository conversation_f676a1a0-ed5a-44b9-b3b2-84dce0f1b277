{"name": "venture-seed", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "predev": "npm run typegen", "prebuild": "npm run typegen", "typegen": "sanity schema extract --path=./sanity/extract.json && sanity typegen generate"}, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@sanity/client": "^7.6.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.97.1", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "easymde": "^2.20.0", "lucide-react": "^0.525.0", "markdown-it": "^14.1.0", "next": "^15.4.0-canary.114", "next-auth": "^5.0.0-beta.29", "next-sanity": "^9.8.58-canary.0", "postcss": "^8.4.38", "react": "^19.0.0", "react-dom": "^19.0.0", "sanity": "^3.97.1", "sanity-plugin-markdown": "^5.1.1", "server-only": "^0.0.1", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/markdown-it": "^14.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "typescript": "^5"}}