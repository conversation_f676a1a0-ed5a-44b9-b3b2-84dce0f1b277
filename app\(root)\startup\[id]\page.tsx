import React, { Suspense } from "react";
import { formatDate } from "@/lib/utils";
import { client } from "@/sanity/lib/client";
import { STARTUP_BY_ID_QUERY } from "@/sanity/lib/queries";
import Link from "next/link";
import {Skeleton} from "@/components/ui/skeleton";
import { notFound } from "next/navigation";
import Image from "next/image";
import markdownit from "markdown-it";
import View from "@/components/ui/View";

const md = markdownit();
export const experimental_ppr = true;

const page = async ({ params }: { params: Promise<{ id: string }> }) => {
  const id = (await params).id;
  const post = await client.fetch(STARTUP_BY_ID_QUERY, { id });
  // console.log(post);

  if (!post) return notFound();

  const parsedContent = md.render(post.content || "");

  return (
    <>
      <section className="pink_container !min-h-[230px]">
        <p className="tag">{formatDate(post?._createdAt)} </p>
        <h1 className="heading"> {post.title} </h1>
        <p className="sub-heading !max-w-5xl">{post.description}</p>
      </section>

      <section className="section_container">
        <img
          src={post.image}
          alt="thumbnail"
          className="w-full h-auto rounded-xl"
        ></img>

        <div className="mt-10 space-y-5 max-w-4xl mx-auto">
          <div className="flex-between gap-5">
            <Link
              href={`/user/${post.author?._id}`}
              className="flex gap-2 itmes-center mb-3"
            >
              <Image
                src={post.author?.image}
                width={64}
                height={64}
                alt="author-avatar"
                className="rounded-full drop-shadow-lg"
              />

              <div className="ml-4">
                <p className="text-20-medium">{post.author?.name}</p>
                <p className="text-20-medium">@{post.author?.username}</p>
              </div>
            </Link>

            <p className="category-tag">{post.category}</p>
          </div>
          <p className="text-30-bold">Pitch Details</p>
          {parsedContent ? (
            <article
              className="prose max-w-4xl font-work-sans break-all"
              dangerouslySetInnerHTML={{ __html: parsedContent }}
            />
          ) : (
            <p>No details provided</p>
          )}
        </div>

        <hr className="divider"/>

        {/* Todo: Editor Selected Startups */}
      </section>

      <Suspense fallback={<Skeleton className="view_skeleton"/>}>
          <View id={id}/>
      </Suspense>
    </>
  );
};

export default page;
